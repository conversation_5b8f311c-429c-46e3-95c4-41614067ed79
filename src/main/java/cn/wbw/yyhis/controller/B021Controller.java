package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B021Converter;
import cn.wbw.yyhis.model.dto.B021DTO;
import cn.wbw.yyhis.model.dto.History;
import cn.wbw.yyhis.model.dto.integration.HospitalDataPacket;
import cn.wbw.yyhis.model.entity.B021;
import cn.wbw.yyhis.service.B021Service;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;

/**
 * <p>
 * 患者就诊基本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b021")
@RequiredArgsConstructor
@Tag(name = "B021", description = "患者就诊基本信息表")
public class B021Controller {

    private final B021Service b021Service;
    private final B021Converter converter = B021Converter.INSTANCE;

    @Operation(summary = "根据患者ID查询患者就诊基本信息")
    @GetMapping("/history")
    public History history(@RequestParam String visitSn) {
        History history = new History();
        B021 one = b021Service.lambdaQuery().eq(B021::getVisitSn, visitSn).one();
        if (one == null) {
            return history;
        }
        B021DTO b021DTO = converter.entityToDto(one);
        history.setPatientId(one.getPatientId());
        history.setVisitSn(one.getVisitSn());
        HospitalDataPacket dataPacket = new HospitalDataPacket();
        dataPacket.setTableCode(B021.TABLE_NAME);
        dataPacket.setData(Collections.singletonList(b021DTO));
        history.setDataPacket(Collections.singletonList(dataPacket));

        return history;
    }

}
