package cn.wbw.yyhis.controller;

import cn.wbw.yyhis.converter.B023Converter;
import cn.wbw.yyhis.model.dto.B023DTO;
import cn.wbw.yyhis.model.dto.B023UpsertDTO;
import cn.wbw.yyhis.model.entity.B023;
import cn.wbw.yyhis.service.B023Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 患者诊断记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@RequestMapping("/b023")
@RequiredArgsConstructor
public class B023Controller {

    private final B023Service b023Service;
    private final B023Converter converter = B023Converter.INSTANCE;

    /**
     * 添加患者诊断记录
     * @param dto 数据传输对象
     * @return
     */
    @PostMapping("/add")
    public B023DTO add(@RequestBody B023UpsertDTO dto) {
        B023 newB023 = b023Service.addPatientDiagnosis(dto);
        return converter.entityToDto(newB023);
    }

    /**
     * 根据复合主键删除
     * @param diagId 诊断ID号
     * @param diagSource 诊断数据来源
     * @return
     */
    @DeleteMapping("/delete/{diagId}/{diagSource}")
    public Boolean delete(@PathVariable String diagId, @PathVariable String diagSource) {
        return b023Service.removeByCompositeId(diagId, diagSource);
    }

    @PutMapping("/update/{diagId}/{diagSource}")
    public Boolean update(@PathVariable String diagId, @PathVariable String diagSource, @RequestBody B023UpsertDTO dto) {
        dto.setDiagId(diagId);
        dto.setDiagSource(diagSource);
        return b023Service.updatePatientDiagnosis(dto);
    }

    @GetMapping("/list")
    public List<B023DTO> listByVisitSn(@RequestParam String visitSn, @RequestParam(required = false) String diagType) {
        List<B023> list = b023Service.lambdaQuery().eq(B023::getVisitSn, visitSn)
                .eq(StringUtils.hasText(diagType), B023::getDiagType, diagType)
                .orderByDesc(B023::getRecordDatetime)
                .list();
        return converter.entityListToDtoList(list);
    }


    @GetMapping("/detail/{diagId}/{diagSource}")
    public B023DTO findById(@PathVariable String diagId, @PathVariable String diagSource) {
        return converter.entityToDto(b023Service.getByCompositeId(diagId, diagSource));
    }

    @GetMapping("/page")
    public Page<B023DTO> findPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                  @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<B023> page = new Page<>(pageNum, pageSize);
        Page<B023> entityPage = b023Service.page(page);
        Page<B023DTO> dtoPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        dtoPage.setRecords(converter.entityListToDtoList(entityPage.getRecords()));
        return dtoPage;
    }
} 