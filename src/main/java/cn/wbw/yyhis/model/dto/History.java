package cn.wbw.yyhis.model.dto;

import cn.wbw.yyhis.model.dto.integration.HospitalDataPacket;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 患者就诊基本信息表 DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Data
public class History {
    /**
     * 患者id
     */
    private String patientId;
    /**
     * 单次就诊唯一标识号
     */
    private String visitSn;

    private List<HospitalDataPacket> dataPacket;
}
